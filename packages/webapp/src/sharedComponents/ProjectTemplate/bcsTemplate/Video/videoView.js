/* eslint-disable @next/next/no-img-element */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import Lightbox from 'sharedComponents/lightBox';
import Style from '../Style/video.module.scss';
import TrapezoidLeft from '../trapezoid';
import InlineSvg from 'sharedComponents/inline-svg';

class BCSVideo extends PureComponent {
  constructor() {
    super();
    this.state = {
      isOpenLightbox: false,
      slide: 0,
    };
  }

  openLightBox = (index) => {
    const { isOpenLightbox } = this.state;
    this.setState({ isOpenLightbox: !isOpenLightbox, slide: index });
  };

  render() {
    const { videos, isOpenInfoModal, snapStatus } = this.props;
    const { slide, isOpenLightbox } = this.state;
    const videoUrl = [];

    const videoData = videos.map((item, index) => {
      // Regex check for youtube/vimeo
      const youtubeMatch = (item.url || '').match(
        /(?:youtube\.com\/(?:[^/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?/\\s]{11})/,
      );
      const vimeoMatch = (item.url || '').match(
        /(?:vimeo\.com\/(?:video\/)?)(\d+)/,
      );

      if (youtubeMatch) {
        const videoId = youtubeMatch[1];
        videoUrl.push(
          <div data-cy="videoSrc" key={`yt-${videoId}`}>
            <iframe
              width="760"
              height="400"
              title="projectVideoFrame"
              src={`https://www.youtube.com/embed/${videoId}`}
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            />
          </div>,
        );
        return (
          <div key={videoId} className="col-12 m0Auto">
            <div className={`${Style.youtubeVideo}`}>
              <img
                data-cy="videoImageThumbnail"
                src={`http://img.youtube.com/vi/${videoId}/hqdefault.jpg`}
                alt="videoPic"
                className={`${Style.imagebox}`}
                onClick={() => this.openLightBox(index + 1)}
                onKeyDown={() => this.openLightBox(index + 1)}
              />
              <i>
                <InlineSvg
                  src="/assets/svg/playButton.svg"
                  alt="videoPic"
                  width="80px"
                  height="60px"
                  onClick={() => this.openLightBox(index + 1)}
                  onKeyDown={() => this.openLightBox(index + 1)}
                />
              </i>
            </div>
            <p className="p font-weight-bold text-white text-center mb-2">
              {item.title}
            </p>
            <p className="p text-white text-center">{item.disc}</p>
          </div>
        );
      } else if (vimeoMatch) {
        const videoId = vimeoMatch[1];
        videoUrl.push(
          <iframe
            key={`vim-${videoId}`}
            width="760"
            height="400"
            title="projectVideoFrame"
            src={`https://player.vimeo.com/video/${videoId}`}
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          />,
        );
        return (
          <div key={videoId} className="col-12 m0Auto">
            <div className={`${Style.youtubeVideo}`}>
              <img
                src={`https://vumbnail.com/${videoId}.jpg`}
                alt="videoPic"
                className={`${Style.imagebox}`}
                onClick={() => this.openLightBox(index + 1)}
                onKeyDown={() => this.openLightBox(index + 1)}
              />
              <i>
                <InlineSvg
                  src="/assets/svg/playButton.svg"
                  alt="videoPic"
                  width="80px"
                  height="60px"
                  onClick={() => this.openLightBox(index + 1)}
                  onKeyDown={() => this.openLightBox(index + 1)}
                />
              </i>
            </div>
            <p className="p font-weight-bold text-white text-center mb-2">
              {item.title}
            </p>
            <p className="p text-white text-center">{item.disc}</p>
          </div>
        );
      } else {
        // Unsupported URL case
        return (
          <div key={`unsupported-${index}`} className="col-12 m0Auto">
            <p className="text-danger text-center">
              Unsupported video URL: {item.url}
            </p>
          </div>
        );
      }
    });

    return (
      <>
        <TrapezoidLeft
          renderComponent={videoData}
          sectionName="PROJECT VIDEOS"
          trapezoidType="right"
          bgColour="bg-secondary"
          headerStatus
          isOpenInfoModal={isOpenInfoModal}
          infoIconStatus={!snapStatus}
        />
        <div className="">
          <Lightbox
            isVisible={isOpenLightbox}
            urls={videoUrl}
            slide={slide}
            type="video"
          />
        </div>
      </>
    );
  }
}

BCSVideo.propTypes = {
  videos: PropTypes.array.isRequired,
  isOpenInfoModal: PropTypes.func.isRequired,
  snapStatus: PropTypes.bool.isRequired,
};

export default BCSVideo;
