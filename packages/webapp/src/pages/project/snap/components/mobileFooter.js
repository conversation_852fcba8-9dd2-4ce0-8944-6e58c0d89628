/* eslint-disable @next/next/no-img-element */
import React from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import Style from '../styles/snapshot.module.scss';
import InlineSvg from 'sharedComponents/inline-svg';

// Mobile footer for snap.
class MobileFooter extends React.PureComponent {
  render() {
    const { snapHashData } = this.props;
    const title = _.get(snapHashData, 'cover.title');
    const regNo = _.get(snapHashData, 'regNo');
    const name = _.get(snapHashData, 'contactDetails.fullName');
    // const email = _.get(snapHashData, 'contactDetails.email');
    return (
      <div className={`${Style.mobileFooterContainer} row mr-0 ml-0 mt-5`}>
        <div className="col-12 p-4">
          <div className={`${Style.mobileTitle}`}>{title}</div>
          <div className={`${Style.mobileProjectRegText} mt-2`}>
            Project identifier #{regNo}
          </div>
        </div>
        <div className="col-12 pl-4 pb-4 pr-4">
          <div className={`${Style.mobileTitle}`}>{name}</div>
        </div>
        <div className="col-12 pl-4 pb-4 pr-4">
          <InlineSvg
            src="/assets/svg/smashlogowhitenew.svg"
            width="160px"
            className={`${Style.logo}`}
            alt=""
          />
        </div>
      </div>
    );
  }
}
MobileFooter.propTypes = {
  snapHashData: PropTypes.object.isRequired,
};

export default MobileFooter;
