@import '../../../../styles/variable.scss';
@import '../../../../styles/mixins.scss';

.shareBgContainer {
  background-color: $theme-black;
  padding-bottom: 100px;
  position: absolute;
  right: 0;
  overflow: hidden;
}

.headerContainer {
  width: 100%;
  background-color: var(--navy, #05012d);
  color: #ffff;
  font-family: 'maisonNeue' !important;
  font-size: 14px !important;
  font-style: normal;
  padding: 12px 24px;
  font-weight: 400;
  line-height: 150%; /* 21px */
}

.iconContainer {
  position: absolute;
  z-index: 10;
}

.headerSquareContainer {
  display: inline-block;
}

.headingContainer {
  text-align: center;
  padding-bottom: 42px;
  @media (max-width: $breakpoint-lg) {
    margin: 0 auto;
  }
}

.rectangle {
  height: 17px;
  background-color: $theme-left-container-bg;
  padding-left: 16px;
  padding-right: 16px;
}

.headingText {
  color: $theme-left-container-bg;
  font-size: 24px;
  font-weight: bold;
  letter-spacing: 1.5px;
  line-height: 32px;
  position: relative;
  text-transform: uppercase;
  font-family: 'robotoCondensed';
}

.hrBorder {
  box-sizing: border-box;
  border: 1px solid $theme-left-container-bg;
  margin-top: 56px;
  // margin-right: 94px;
  min-width: 70%;
  margin-left: 40px;
  margin-right: 40px;
  margin-bottom: 0px !important;

  // margin: 0 auto;
}

.lockContainer {
  margin-right: 20px;
  margin-bottom: 10px;
}

.footerRow {
  border-right: 1px solid $form_field-text;
  @media (max-width: $breakpoint-lg) {
    border-right: 0px solid $form_field-text;
  }
  @media (min-width: $breakpoint-lg) and (max-width: $breakpoint-xl) {
    border-right: 0px solid #e4e4e4;
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.footerArea {
  @media (min-width: $breakpoint-lg) and (max-width: $breakpoint-xl) {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.footerContainer {
  background-color: $desc-bg;
  min-height: 300px;
  margin-top: 56px;
  padding-top: 82px;
  padding-bottom: 160px;
  flex-direction: row !important;
  @media (max-width: $breakpoint-lg) {
    flex-direction: column !important;
  }
  @media (min-width: $breakpoint-lg) and (max-width: $breakpoint-xl) {
    flex-direction: column !important;
  }
}

.footerHeadingText {
  color: $form_field-text;
  font-size: 24px;
  letter-spacing: 0.5px;
  line-height: 50px;
  font-weight: bold;
  @media (max-width: $breakpoint-lg) {
    text-align: center !important;
    border-top: 1px solid $form_field-text;
    margin: 20px;
  }
  @media (min-width: $breakpoint-lg) and (max-width: $breakpoint-xl) {
    text-align: center !important;
    border-top: 1px solid $form_field-text;
    margin: 20px;
  }
}

.footerTitleText {
  color: $form_field-text;
  font-size: 24px;
  letter-spacing: 0.5px;
  font-weight: bold;
  @media (max-width: $breakpoint-lg) {
    text-align: center !important;
    max-width: 100%;
  }
  @media (min-width: $breakpoint-lg) and (max-width: $breakpoint-xl) {
    text-align: center !important;
  }
}

.footerText {
  color: $form_field-text;
  font-size: 18px;
  letter-spacing: 0.5px;
  line-height: 24px;
  @media (max-width: $breakpoint-lg) {
    text-align: center !important;
  }
  @media (min-width: $breakpoint-lg) and (max-width: $breakpoint-xl) {
    text-align: center !important;
  }
}
.footerImage {
  @media (max-width: $breakpoint-lg) {
    text-align: center !important;
  }
  @media (min-width: $breakpoint-lg) and (max-width: $breakpoint-xl) {
    text-align: center !important;
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.rotateIcon {
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
  margin-top: 8px;
}

.rotateShowIcon {
  -webkit-transform: rotate(225deg);
  -moz-transform: rotate(225deg);
  -ms-transform: rotate(225deg);
  -o-transform: rotate(225deg);
  transform: rotate(225deg);
  margin-top: 8px;
}

.centerAlign {
  margin: 0 auto;
}

.leftBarContainer {
  background-color: $leftBar-bg;
  position: fixed !important;
  margin-top: 60px;
}

.backGroundImageFooter {
  // background-image: url('/assets/png/snapFooter.png');
  background-color: #05012d;
  /* Full height */
  min-height: 100%;

  /* Center and scale the image nicely */
  background-position: center;
  background-repeat: no-repeat;
  // background-size: cover;
  background-size: 100% 100%;
}

.anchorText {
  color: #9a56c7;
}

/* mouse over link */
.anchorText:hover {
  color: #9a56c7;
}

.authRowContainer {
  margin-bottom: 272px;
}

/*********************************************************
* Snap Auth screen css
**********************************************************/
.mainAuthContainer {
  display: flex;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding-top: 56px;
  @media (max-width: $breakpoint-sm) {
    padding-top: 26px !important;
    padding-left: 26px !important;
    padding-right: 26px !important;
  }
}

.shadowBox {
  margin-right: 0px; /* Set to 0 if you don't want shadow at the right side */
  margin-left: 0px; /* Set to 20px if you want shadow at the left side */
  margin-top: 150px; /* Set to 20px if you want shadow at the top side */
  margin-bottom: 0px; /* Set to 20px if you want shadow at the bottom side */
  box-shadow: 0px 0px 36px 90px $theme-black;
  max-height: 50%;
  max-width: 100%;
  z-index: 999;
}

.shadowContainer {
  margin: 0 auto;
  display: table;
  overflow: hidden;
  position: absolute;
  width: 100%;
  background-color: $theme-black;
}

.coverBox {
  margin: auto 0;
  color: #e4e4e4;
  width: 450px;
  height: 450px;
  text-align: center;
  border: 4px solid $theme-border-color;
}

.producerContainer {
  border-bottom: 4px solid $theme-border-color;
  margin: 0;
}

.headers {
  font-size: 20px;
  font-weight: 300;
  word-wrap: break-word;
  color: $theme-left-container-bg;
  font-family: 'robotoCondensed';
  letter-spacing: 0;
  line-height: 30px;
  margin-bottom: 0px;
  @media (max-width: 767px) {
    font-size: 15px;
  }
}

.producerNameText {
  color: $theme-left-container-bg;
  font-size: 32px;
  margin-left: 20px;
  word-wrap: break-word;
  max-width: 300px;
  text-transform: uppercase;
  line-height: 30px;
  font-family: 'robotoCondensed';
  margin-bottom: 0px;
}

.titleContainer {
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title {
  color: $theme-left-container-bg;
  font-size: 60px;
  font-weight: bold;
  padding: 30px;
  white-space: initial;
  vertical-align: middle;
  letter-spacing: 1.5px;
  line-height: 65px;
  font-family: 'PettingillCFBold';
}

.bottomBox {
  border-top: 4px solid $theme-border-color;
  margin: 0;
}

.director {
  margin-right: 0px !important;
  justify-content: center;
  border-right: 4px solid $theme-border-color;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  @media (max-width: 767px) {
    border-right: none;
    border-bottom: 4px solid $theme-border-color;
  }
}

.writer {
  margin-left: 0px !important;
  justify-content: center;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
}

.signHeader {
  font-style: normal;
  font-weight: normal;
  font-size: 32px;
  line-height: 37px;
  text-align: center;
  letter-spacing: 0.5px;
  color: #e4e4e4;
}

.btnContainer {
  width: 24%;
  padding-top: 46px;
  margin: 0 auto;
  @media (max-width: $breakpoint-sm) {
    width: 200px !important;
  }
}

.allreadyAccountContainer {
  margin-top: 40px;
}

.allreadyAccountText {
  font-style: normal;
  font-weight: normal;
  font-size: 18px;
  line-height: 24px;
  text-align: center;
  color: #e4e4e4;
}

.linkbtn {
  color: $magenta;
  background: transparent;
  border: none;
}

.bgContainer {
  height: 400px;

  @media (max-width: 767px) {
    background-position: center top !important;
    background-size: cover !important;
  }
}

.bcsContainer {
  margin-top: 296px;
  @media (max-width: $breakpoint-sm) {
    margin-top: 100px !important;
  }
}

.disable {
  pointer-events: none;
  cursor: default;
}

/*********************************************************
* Mobile Footer css
**********************************************************/

.mobileFooterContainer {
  background-color: $navy;
}

.mobileTitle {
  font-family: 'chaney';
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 19px;
  color: #ffffff;
}

.mobileProjectRegText {
  font-family: 'maisonNeue';
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 16px;
  color: #ffffff;
}
