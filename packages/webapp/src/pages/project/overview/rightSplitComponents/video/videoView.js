/* eslint-disable @next/next/no-img-element */
import React from 'react';
import PropTypes from 'prop-types';
import Lightbox from 'sharedComponents/lightBox';
import Style from '../../style/projectDashboard.module.scss';
import InlineSvg from 'sharedComponents/inline-svg';
import { isEmpty } from 'lodash';

// Component show project videos

class VideoView extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      isOpenLightbox: false,
      slide: 0,
    };
  }

  // This function take index of video and set lightbox open status.

  openLightBox = (index) => {
    const { isOpenLightbox } = this.state;
    this.setState({ isOpenLightbox: !isOpenLightbox, slide: index });
  };

  render() {
    const { videos } = this.props;
    const { slide, isOpenLightbox } = this.state;
    const videoUrl = [];

    const projectsVideo =
      videos.length &&
      videos.map((item, index) => {
        if (isEmpty(item.url)) {
          return (
            <div key={`unsupported-${index}`} className="m0Auto">
              <p className="text-danger text-center">
                Unsupported video URL: {item.url}
              </p>
            </div>
          );
        }

        // Regex check for youtube/vimeo
        const youtubeMatch = item.url.match(
          /(?:youtube\.com\/(?:[^/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?/\\s]{11})/,
        );
        const vimeoMatch = item.url.match(/(?:vimeo\.com\/(?:video\/)?)(\d+)/);

        if (youtubeMatch) {
          const videoId = youtubeMatch[1];
          videoUrl.push(
            <div data-cy="videoSrc" key={`yt-${videoId}`}>
              <iframe
                width="760"
                height="400"
                title="projectVideoFrame"
                src={`https://www.youtube.com/embed/${videoId}`}
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              />
            </div>,
          );

          return (
            <div key={videoId} className="m0Auto">
              <div className={`${Style.showformVideosRow}`}>
                <div className="m0Auto">
                  <div className={`${Style.videoImageContainer}`}>
                    <div>
                      <div className={`${Style.youtubeVideo}`}>
                        <img
                          data-cy="videoImageThumbnail"
                          src={`http://img.youtube.com/vi/${videoId}/hqdefault.jpg`}
                          alt="videoPic"
                          className={Style.imagebox}
                          onClick={() => this.openLightBox(index + 1)}
                          onKeyDown={() => this.openLightBox(index + 1)}
                        />
                        <i>
                          <InlineSvg
                            src="/assets/svg/playButton.svg"
                            alt="videoPic"
                            width="800px"
                            height="60px"
                            onClick={() => this.openLightBox(index + 1)}
                            onKeyDown={() => this.openLightBox(index + 1)}
                          />
                        </i>
                      </div>
                      <div
                        className={`${Style.videoTitle}`}
                        data-cy="videoTitle"
                      >
                        {item.title}
                      </div>
                      <div className={`${Style.videoDesc}`} data-cy="videoDesc">
                        {item.disc}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          );
        } else if (vimeoMatch) {
          const videoId = vimeoMatch[1];
          videoUrl.push(
            <iframe
              key={`vim-${videoId}`}
              width="760"
              height="400"
              title="projectVideoFrame"
              src={`https://player.vimeo.com/video/${videoId}`}
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            />,
          );

          return (
            <div key={videoId} className="m0Auto">
              <div className={`${Style.showformVideosRow}`}>
                <div className="m0Auto">
                  <div className={`${Style.videoImageContainer}`}>
                    <div>
                      <div className={`${Style.youtubeVideo}`}>
                        <img
                          src={`https://vumbnail.com/${videoId}.jpg`}
                          alt="videoPic"
                          className={Style.imagebox}
                          onClick={() => this.openLightBox(index + 1)}
                          onKeyDown={() => this.openLightBox(index + 1)}
                        />
                        <i>
                          <InlineSvg
                            src="/assets/svg/playButton.svg"
                            alt="videoPic"
                            width="800px"
                            height="60px"
                            onClick={() => this.openLightBox(index + 1)}
                            onKeyDown={() => this.openLightBox(index + 1)}
                          />
                        </i>
                      </div>
                      <div className={`${Style.videoTitle}`}>{item.title}</div>
                      <div className={`${Style.videoDesc}`}>{item.disc}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          );
        } else {
          return (
            <div key={`unsupported-${index}`} className="m0Auto">
              <p className="text-danger text-center">
                Unsupported video URL: {item.url}
              </p>
            </div>
          );
        }
      });

    return (
      <div className="pt-4">
        {videos.length > 0 && projectsVideo}
        <div className="">
          <Lightbox
            isVisible={isOpenLightbox}
            urls={videoUrl}
            slide={slide}
            type="video"
          />
        </div>
      </div>
    );
  }
}

VideoView.propTypes = {
  videos: PropTypes.array.isRequired,
};

export default VideoView;
